# API连接测试指南

## 🎯 验证前端修改是否生效

### 1. 检查环境变量
在浏览器控制台输入：
```javascript
console.log('VITE_USE_MOCK_API:', import.meta.env.VITE_USE_MOCK_API);
console.log('API Base URL:', import.meta.env.VITE_API_BASE_URL);
```

**预期结果**：
- `VITE_USE_MOCK_API: "false"`
- `API Base URL: "http://localhost:8000/api"`

### 2. 检查API选择逻辑
在浏览器控制台输入：
```javascript
// 检查当前使用的API类型
import('/src/services/api.js').then(module => {
  console.log('当前API类型:', module.api === module.mockAPI ? '模拟API' : '真实API');
});
```

**预期结果**：应该显示 `"当前API类型: 真实API"`

### 3. 测试网络请求
1. 打开浏览器开发者工具的 Network 面板
2. 进入面试页面，输入回答并提交
3. 观察网络请求

**预期结果**：
- 应该看到对 `http://localhost:8000/api/analyze` 的 POST 请求
- 如果后端未启动，会看到连接失败的错误

## 🚀 后端开发快速开始

### 最简单的测试后端
创建一个简单的测试服务器来验证连接：

```javascript
// test-server.js
const express = require('express');
const cors = require('cors');

const app = express();
app.use(cors());
app.use(express.json());

app.post('/api/analyze', (req, res) => {
  const { question, answer } = req.body;
  
  console.log('收到请求:', { question, answer });
  
  // 简单的测试响应
  res.json({
    success: true,
    data: {
      needFollowUp: answer.length < 50,
      feedback: answer.length < 50 
        ? "测试：需要追问 - 请详细说明" 
        : "测试：回答满意 - 谢谢"
    },
    timestamp: new Date().toISOString()
  });
});

app.listen(8000, () => {
  console.log('测试服务器运行在 http://localhost:8000');
});
```

### 运行测试服务器
```bash
# 安装依赖
npm init -y
npm install express cors

# 运行服务器
node test-server.js
```

## 📋 完整测试流程

### 步骤1：确认前端配置
- ✅ `VITE_USE_MOCK_API=false`
- ✅ `VITE_API_BASE_URL=http://localhost:8000/api`

### 步骤2：重启前端服务
```bash
cd frontend
npm run dev
```

### 步骤3：启动后端服务
```bash
# 在另一个终端
node test-server.js
```

### 步骤4：测试连接
1. 打开 http://localhost:5174
2. 开始面试
3. 输入回答并提交
4. 检查：
   - 浏览器 Network 面板有请求
   - 后端控制台有日志
   - 前端收到响应

## 🔧 故障排除

### 问题1：仍然使用模拟API
**解决**：
1. 确认 `.env.development` 文件中 `VITE_USE_MOCK_API=false`
2. 重启前端开发服务器
3. 清除浏览器缓存

### 问题2：网络请求失败
**可能原因**：
- 后端服务未启动
- 端口被占用
- CORS配置问题

**解决**：
1. 确认后端在 8000 端口运行
2. 检查后端日志
3. 确认CORS中间件已配置

### 问题3：请求格式错误
**检查**：
- 请求Content-Type是否为 `application/json`
- 请求体格式是否正确
- 响应格式是否符合要求

## ✅ 成功标志

当一切正常时，您应该看到：

1. **浏览器控制台**：显示使用真实API
2. **Network面板**：有到后端的HTTP请求
3. **后端日志**：收到前端请求
4. **前端界面**：显示后端返回的反馈

## 🎯 下一步

连接测试成功后，您可以：
1. 替换测试后端为您的正式实现
2. 集成真实的AI分析逻辑
3. 添加更复杂的业务逻辑
4. 完善错误处理和日志记录
