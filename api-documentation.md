# Kora 语音面试官 API 文档

## 概述

本文档描述了 Kora 语音面试官系统的后端 API 接口规范。前端通过这些接口与后端进行交互，实现智能面试功能。

## 基础信息

- **Base URL**: `http://localhost:3000/api`
- **Content-Type**: `application/json`
- **字符编码**: UTF-8

## 接口列表

### 1. 提交用户回答并获取AI反馈

#### 接口描述
用户完成语音回答后，前端将回答内容发送给后端，后端调用大模型分析回答并决定下一步操作（追问或下一题）。

#### 请求信息
- **URL**: `/interview/analyze`
- **Method**: `POST`
- **Content-Type**: `application/json`

#### 请求参数

```json
{
  "questionIndex": 0,
  "question": "你最近完成的一件最有成就感的事是什么？你在其中扮演了什么角色？",
  "answer": "我最近完成了一个团队项目，负责前端开发...",
  "sessionId": "uuid-session-id",
  "interviewStyle": "professional"
}
```

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| questionIndex | number | 是 | 当前问题索引（0-2） |
| question | string | 是 | 当前问题内容 |
| answer | string | 是 | 用户的回答内容 |
| sessionId | string | 是 | 面试会话ID，用于追踪整个面试过程 |
| interviewStyle | string | 否 | 面试风格：professional/friendly/campus，默认professional |

#### 响应格式

**成功响应 (200)**
```json
{
  "success": true,
  "data": {
    "action": "follow_up",
    "feedback": "很好的回答！能再详细说说你在项目中遇到的具体挑战吗？",
    "nextQuestion": null,
    "shouldContinue": true,
    "analysis": {
      "completeness": 7,
      "clarity": 8,
      "relevance": 9,
      "suggestions": ["可以更详细描述具体的技术挑战", "建议量化项目成果"]
    }
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

**下一题响应示例**
```json
{
  "success": true,
  "data": {
    "action": "next_question",
    "feedback": "明白了，谢谢你的详细回答。",
    "nextQuestion": "请讲讲一次你解决冲突或困难的经历。",
    "shouldContinue": true,
    "analysis": {
      "completeness": 9,
      "clarity": 9,
      "relevance": 10,
      "suggestions": []
    }
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

**面试结束响应示例**
```json
{
  "success": true,
  "data": {
    "action": "interview_complete",
    "feedback": "感谢您参与本次面试，您的回答都很出色！",
    "nextQuestion": null,
    "shouldContinue": false,
    "analysis": {
      "overallScore": 85,
      "strengths": ["沟通能力强", "技术基础扎实", "团队协作意识好"],
      "improvements": ["可以更多展示领导力", "建议准备更多具体案例"]
    }
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

#### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| data.action | string | 下一步操作：follow_up(追问)/next_question(下一题)/interview_complete(面试结束) |
| data.feedback | string | AI生成的反馈内容 |
| data.nextQuestion | string\|null | 下一个问题内容（action为next_question时有值） |
| data.shouldContinue | boolean | 是否继续面试 |
| data.analysis | object | 回答分析结果 |
| data.analysis.completeness | number | 回答完整度评分（1-10） |
| data.analysis.clarity | number | 回答清晰度评分（1-10） |
| data.analysis.relevance | number | 回答相关性评分（1-10） |
| data.analysis.suggestions | array | 改进建议列表 |
| timestamp | string | 响应时间戳 |

#### 错误响应

**400 Bad Request**
```json
{
  "success": false,
  "error": {
    "code": "INVALID_PARAMS",
    "message": "请求参数不完整或格式错误",
    "details": {
      "missingFields": ["answer"],
      "invalidFields": []
    }
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

**500 Internal Server Error**
```json
{
  "success": false,
  "error": {
    "code": "AI_SERVICE_ERROR",
    "message": "AI服务暂时不可用，请稍后重试",
    "details": {}
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### 2. 开始新面试会话

#### 接口描述
开始新的面试会话，获取会话ID和第一个问题。

#### 请求信息
- **URL**: `/interview/start`
- **Method**: `POST`
- **Content-Type**: `application/json`

#### 请求参数

```json
{
  "interviewStyle": "professional",
  "candidateName": "张三"
}
```

#### 响应格式

```json
{
  "success": true,
  "data": {
    "sessionId": "uuid-session-id",
    "firstQuestion": "你最近完成的一件最有成就感的事是什么？你在其中扮演了什么角色？",
    "totalQuestions": 3,
    "estimatedDuration": "5-8分钟"
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### 3. 获取面试总结

#### 接口描述
获取完整的面试总结报告。

#### 请求信息
- **URL**: `/interview/summary/{sessionId}`
- **Method**: `GET`

#### 响应格式

```json
{
  "success": true,
  "data": {
    "sessionId": "uuid-session-id",
    "candidateName": "张三",
    "interviewStyle": "professional",
    "startTime": "2024-01-20T10:25:00Z",
    "endTime": "2024-01-20T10:32:00Z",
    "duration": "7分钟",
    "overallScore": 85,
    "questions": [
      {
        "questionIndex": 0,
        "question": "你最近完成的一件最有成就感的事是什么？",
        "answer": "我最近完成了一个团队项目...",
        "followUps": [
          {
            "question": "能详细说说遇到的挑战吗？",
            "answer": "主要挑战是技术选型..."
          }
        ],
        "analysis": {
          "completeness": 9,
          "clarity": 8,
          "relevance": 9
        }
      }
    ],
    "summary": {
      "strengths": ["沟通能力强", "技术基础扎实"],
      "improvements": ["可以更多展示领导力"],
      "recommendation": "建议进入下一轮面试"
    }
  },
  "timestamp": "2024-01-20T10:32:00Z"
}
```

## 错误码说明

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| INVALID_PARAMS | 请求参数错误 | 检查请求参数格式和必填字段 |
| SESSION_NOT_FOUND | 会话不存在 | 重新开始面试会话 |
| AI_SERVICE_ERROR | AI服务错误 | 稍后重试或联系技术支持 |
| RATE_LIMIT_EXCEEDED | 请求频率超限 | 降低请求频率 |
| INTERNAL_ERROR | 服务器内部错误 | 联系技术支持 |

## 使用示例

### JavaScript/Axios 示例

```javascript
// 提交用户回答
const analyzeAnswer = async (questionData) => {
  try {
    const response = await axios.post('/api/interview/analyze', {
      questionIndex: 0,
      question: "你最近完成的一件最有成就感的事是什么？",
      answer: "我最近完成了一个团队项目...",
      sessionId: "current-session-id",
      interviewStyle: "professional"
    });
    
    return response.data;
  } catch (error) {
    console.error('API调用失败:', error);
    throw error;
  }
};
```

## 注意事项

1. **会话管理**: 每次面试需要先调用 `/interview/start` 获取 sessionId
2. **错误处理**: 建议实现重试机制，特别是对于 AI_SERVICE_ERROR
3. **超时设置**: 建议设置30秒的请求超时时间
4. **数据验证**: 前端应验证用户输入的完整性
5. **安全考虑**: 生产环境需要添加认证和授权机制

## 后端实现示例

### Node.js + Express 基础框架

```javascript
// server.js
const express = require('express');
const cors = require('cors');
const { v4: uuidv4 } = require('uuid');

const app = express();
app.use(cors());
app.use(express.json());

// 模拟会话存储
const sessions = new Map();

// 开始面试
app.post('/api/interview/start', (req, res) => {
  const { interviewStyle, candidateName } = req.body;
  const sessionId = uuidv4();

  sessions.set(sessionId, {
    sessionId,
    candidateName,
    interviewStyle,
    startTime: new Date().toISOString(),
    questions: [],
    currentQuestionIndex: 0
  });

  res.json({
    success: true,
    data: {
      sessionId,
      firstQuestion: "你最近完成的一件最有成就感的事是什么？",
      totalQuestions: 3,
      estimatedDuration: "5-8分钟"
    },
    timestamp: new Date().toISOString()
  });
});

// 分析回答
app.post('/api/interview/analyze', async (req, res) => {
  try {
    const { questionIndex, question, answer, sessionId } = req.body;

    // 调用大模型API
    const aiResponse = await callLLMAPI(question, answer, questionIndex);

    res.json({
      success: true,
      data: aiResponse,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: 'AI_SERVICE_ERROR',
        message: 'AI服务暂时不可用，请稍后重试'
      }
    });
  }
});

app.listen(3000, () => {
  console.log('API服务器运行在 http://localhost:3000');
});
```

### 大模型集成示例（通义千问）

```javascript
// llm-service.js
const axios = require('axios');

async function callLLMAPI(question, answer, questionIndex) {
  const prompt = `
作为专业的面试官，请分析候选人的回答并决定下一步动作。

问题：${question}
回答：${answer}

请根据回答质量决定：
1. 如果回答不够详细，返回追问
2. 如果回答充分，进入下一题
3. 如果是最后一题，结束面试

返回JSON格式：
{
  "action": "follow_up|next_question|interview_complete",
  "feedback": "反馈内容",
  "nextQuestion": "下一题内容（如适用）",
  "analysis": {
    "completeness": 评分1-10,
    "clarity": 评分1-10,
    "relevance": 评分1-10
  }
}
  `;

  // 调用通义千问API
  const response = await axios.post('https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', {
    model: "qwen-turbo",
    input: { prompt },
    parameters: {
      temperature: 0.7,
      max_tokens: 500
    }
  }, {
    headers: {
      'Authorization': `Bearer ${process.env.DASHSCOPE_API_KEY}`,
      'Content-Type': 'application/json'
    }
  });

  return JSON.parse(response.data.output.text);
}
```

## 部署指南

### 环境变量配置

```bash
# .env
DASHSCOPE_API_KEY=your_api_key_here
DATABASE_URL=your_database_url
JWT_SECRET=your_jwt_secret
PORT=3000
```

### Docker部署

```dockerfile
# Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

### 生产环境注意事项

1. **API密钥安全**: 使用环境变量存储敏感信息
2. **请求限制**: 添加速率限制防止滥用
3. **错误处理**: 完善的错误处理和日志记录
4. **数据库**: 使用持久化存储替代内存存储
5. **监控**: 添加性能监控和告警机制
