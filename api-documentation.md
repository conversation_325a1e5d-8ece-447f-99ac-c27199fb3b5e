# Kora 语音面试官 API 文档

## 概述

本文档描述了 Kora 语音面试官系统的后端 API 接口规范。前端通过这些接口与后端进行交互，实现智能面试功能。

## 基础信息

- **Base URL**: `http://localhost:8000/api`
- **Content-Type**: `application/json`
- **字符编码**: UTF-8

## 接口列表

### 1. 分析用户回答（唯一后端接口）

#### 接口描述
用户完成语音回答后，前端将回答内容发送给后端，后端调用大模型分析回答并决定是否需要追问。

#### 请求信息
- **URL**: `/analyze`
- **Method**: `POST`
- **Content-Type**: `application/json`

#### 请求参数

```json
{
  "question": "你最近完成的一件最有成就感的事是什么？你在其中扮演了什么角色？",
  "answer": "我最近完成了一个团队项目，负责前端开发..."
}
```

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| question | string | 是 | 当前问题内容 |
| answer | string | 是 | 用户的回答内容 |

#### 响应格式

**追问响应 (200)**
```json
{
  "success": true,
  "data": {
    "needFollowUp": true,
    "feedback": "很好的回答！能再详细说说你在项目中遇到的具体挑战吗？"
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

**满意回答响应 (200)**
```json
{
  "success": true,
  "data": {
    "needFollowUp": false,
    "feedback": "明白了，谢谢你的详细回答。"
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

#### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| data.needFollowUp | boolean | 是否需要追问（true=需要追问，false=回答满意） |
| data.feedback | string | AI生成的反馈内容（追问问题或确认信息） |
| timestamp | string | 响应时间戳 |

#### 错误响应

**400 Bad Request**
```json
{
  "success": false,
  "error": {
    "code": "INVALID_PARAMS",
    "message": "请求参数不完整或格式错误",
    "details": {
      "missingFields": ["answer"],
      "invalidFields": []
    }
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

**500 Internal Server Error**
```json
{
  "success": false,
  "error": {
    "code": "AI_SERVICE_ERROR",
    "message": "AI服务暂时不可用，请稍后重试",
    "details": {}
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

## 前端负责的功能

以下功能由前端直接处理，无需后端接口：

### 1. 面试会话管理
- 生成会话ID
- 管理面试状态
- 问题切换逻辑

### 2. 面试记录存储
- 本地存储问答记录
- 生成面试总结
- 导出功能

### 3. 面试流程控制
- 问题顺序管理
- 面试结束判断
- 页面跳转逻辑

## 错误码说明

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| INVALID_PARAMS | 请求参数错误 | 检查请求参数格式和必填字段 |
| SESSION_NOT_FOUND | 会话不存在 | 重新开始面试会话 |
| AI_SERVICE_ERROR | AI服务错误 | 稍后重试或联系技术支持 |
| RATE_LIMIT_EXCEEDED | 请求频率超限 | 降低请求频率 |
| INTERNAL_ERROR | 服务器内部错误 | 联系技术支持 |

## 使用示例

### JavaScript/Axios 示例

```javascript
// 提交用户回答
const analyzeAnswer = async (questionData) => {
  try {
    const response = await axios.post('/api/interview/analyze', {
      questionIndex: 0,
      question: "你最近完成的一件最有成就感的事是什么？",
      answer: "我最近完成了一个团队项目...",
      sessionId: "current-session-id",
      interviewStyle: "professional"
    });
    
    return response.data;
  } catch (error) {
    console.error('API调用失败:', error);
    throw error;
  }
};
```

## 注意事项

1. **会话管理**: 每次面试需要先调用 `/interview/start` 获取 sessionId
2. **错误处理**: 建议实现重试机制，特别是对于 AI_SERVICE_ERROR
3. **超时设置**: 建议设置30秒的请求超时时间
4. **数据验证**: 前端应验证用户输入的完整性
5. **安全考虑**: 生产环境需要添加认证和授权机制

## 后端实现示例

### Node.js + Express 简化实现

```javascript
// server.js
const express = require('express');
const cors = require('cors');

const app = express();
app.use(cors());
app.use(express.json());

// 唯一的分析接口
app.post('/api/analyze', async (req, res) => {
  try {
    const { question, answer } = req.body;

    // 参数验证
    if (!question || !answer) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PARAMS',
          message: '问题和回答内容不能为空'
        }
      });
    }

    // 调用大模型API分析
    const aiResponse = await analyzeAnswer(question, answer);

    res.json({
      success: true,
      data: aiResponse,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('分析回答失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'AI_SERVICE_ERROR',
        message: 'AI服务暂时不可用，请稍后重试'
      }
    });
  }
});

app.listen(8000, () => {
  console.log('API服务器运行在 http://localhost:8000');
});
```

### 大模型集成示例（通义千问）

```javascript
// ai-service.js
const axios = require('axios');

async function analyzeAnswer(question, answer) {
  const prompt = `
作为专业的面试官，请分析候选人的回答并决定是否需要追问。

问题：${question}
回答：${answer}

请根据回答质量决定：
1. 如果回答不够详细或需要更多信息，返回追问问题
2. 如果回答充分满意，返回确认反馈

返回JSON格式：
{
  "needFollowUp": true/false,
  "feedback": "追问问题或确认反馈"
}
  `;

  try {
    // 调用通义千问API
    const response = await axios.post('https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', {
      model: "qwen-turbo",
      input: { prompt },
      parameters: {
        temperature: 0.7,
        max_tokens: 200
      }
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.DASHSCOPE_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    return JSON.parse(response.data.output.text);
  } catch (error) {
    console.error('调用大模型失败:', error);
    // 降级处理：简单的长度判断
    return {
      needFollowUp: answer.length < 50,
      feedback: answer.length < 50
        ? "能再详细说说吗？"
        : "明白了，谢谢你的回答。"
    };
  }
}

module.exports = { analyzeAnswer };
```

## 部署指南

### 环境变量配置

```bash
# .env
DASHSCOPE_API_KEY=your_api_key_here
DATABASE_URL=your_database_url
JWT_SECRET=your_jwt_secret
PORT=3000
```

### Docker部署

```dockerfile
# Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

### 生产环境注意事项

1. **API密钥安全**: 使用环境变量存储敏感信息
2. **请求限制**: 添加速率限制防止滥用
3. **错误处理**: 完善的错误处理和日志记录
4. **数据库**: 使用持久化存储替代内存存储
5. **监控**: 添加性能监控和告警机制
