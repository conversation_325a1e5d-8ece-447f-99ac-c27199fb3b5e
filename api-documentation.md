# Kora 语音面试官 API 文档

## 概述

本文档描述了 Kora 语音面试官系统的后端 API 接口规范。前端通过这些接口与后端进行交互，实现智能面试功能。

## 基础信息

- **Base URL**: `http://localhost:8000/api`
- **Content-Type**: `application/json`
- **字符编码**: UTF-8

## 接口列表

### 1. 分析用户回答（唯一后端接口）

#### 接口描述
用户完成语音回答后，前端将回答内容发送给后端，后端调用大模型分析回答并决定是否需要追问。

#### 请求信息
- **URL**: `/analyze`
- **Method**: `POST`
- **Content-Type**: `application/json`

#### 请求参数

```json
{
  "question": "你最近完成的一件最有成就感的事是什么？你在其中扮演了什么角色？",
  "answer": "我最近完成了一个团队项目，负责前端开发..."
}
```

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| question | string | 是 | 当前问题内容 |
| answer | string | 是 | 用户的回答内容 |

#### 响应格式

**追问响应 (200)**
```json
{
  "success": true,
  "data": {
    "needFollowUp": true,
    "feedback": "很好的回答！能再详细说说你在项目中遇到的具体挑战吗？"
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

**满意回答响应 (200)**
```json
{
  "success": true,
  "data": {
    "needFollowUp": false,
    "feedback": "明白了，谢谢你的详细回答。"
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

#### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| data.needFollowUp | boolean | 是否需要追问（true=需要追问，false=回答满意） |
| data.feedback | string | AI生成的反馈内容（追问问题或确认信息） |
| timestamp | string | 响应时间戳 |

#### 错误响应

**400 Bad Request**
```json
{
  "success": false,
  "error": {
    "code": "INVALID_PARAMS",
    "message": "请求参数不完整或格式错误",
    "details": {
      "missingFields": ["answer"],
      "invalidFields": []
    }
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

**500 Internal Server Error**
```json
{
  "success": false,
  "error": {
    "code": "AI_SERVICE_ERROR",
    "message": "AI服务暂时不可用，请稍后重试",
    "details": {}
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

## 前端负责的功能

以下功能由前端直接处理，无需后端接口：

### 1. 面试会话管理
- 生成会话ID
- 管理面试状态
- 问题切换逻辑

### 2. 面试记录存储
- 本地存储问答记录
- 生成面试总结
- 导出功能

### 3. 面试流程控制
- 问题顺序管理
- 面试结束判断
- 页面跳转逻辑

## 错误码说明

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| INVALID_PARAMS | 请求参数错误 | 检查请求参数格式和必填字段 |
| SESSION_NOT_FOUND | 会话不存在 | 重新开始面试会话 |
| AI_SERVICE_ERROR | AI服务错误 | 稍后重试或联系技术支持 |
| RATE_LIMIT_EXCEEDED | 请求频率超限 | 降低请求频率 |
| INTERNAL_ERROR | 服务器内部错误 | 联系技术支持 |

## 使用示例

### JavaScript/Axios 示例

```javascript
// 提交用户回答
const analyzeAnswer = async (questionData) => {
  try {
    const response = await axios.post('/api/interview/analyze', {
      questionIndex: 0,
      question: "你最近完成的一件最有成就感的事是什么？",
      answer: "我最近完成了一个团队项目...",
      sessionId: "current-session-id",
      interviewStyle: "professional"
    });
    
    return response.data;
  } catch (error) {
    console.error('API调用失败:', error);
    throw error;
  }
};
```

## 注意事项

1. **会话管理**: 每次面试需要先调用 `/interview/start` 获取 sessionId
2. **错误处理**: 建议实现重试机制，特别是对于 AI_SERVICE_ERROR
3. **超时设置**: 建议设置30秒的请求超时时间
4. **数据验证**: 前端应验证用户输入的完整性
5. **安全考虑**: 生产环境需要添加认证和授权机制

## 后端开发指南

### 接口实现要求

您需要实现一个 POST 接口：`/api/analyze`

**核心功能**：
- 接收问题和用户回答
- 调用大模型分析回答质量
- 返回是否需要追问的决策

**实现建议**：
1. **参数验证**：确保问题和回答不为空
2. **AI集成**：调用您选择的大模型API
3. **错误处理**：提供降级方案和友好错误信息
4. **日志记录**：记录请求和响应用于调试

### 响应格式要求

请严格按照以下格式返回：

```json
{
  "success": true,
  "data": {
    "needFollowUp": true,
    "feedback": "很好的回答！能再详细说说你在项目中遇到的具体挑战吗？"
  },
  "timestamp": "2024-01-20T10:30:00Z"
}
```

### 测试建议

1. **简单测试**：先返回固定响应验证前后端连通性
2. **逐步完善**：再集成真实的AI分析逻辑
3. **错误测试**：测试各种错误情况的处理
