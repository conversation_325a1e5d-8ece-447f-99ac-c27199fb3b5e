<template>
  <div class="flex flex-col items-center justify-center min-h-screen bg-gray-50">
    <h1 class="text-2xl font-bold mb-6">你好，我是Kora的语音面试官</h1>
    <p class="mb-8">接下来我会用中文向你提问一些常见面试问题，请用语音作答。</p>
    <button class="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition" @click="startInterview">
      开始面试
    </button>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
const router = useRouter();
function startInterview() {
  router.push('/interview');
}
</script>
