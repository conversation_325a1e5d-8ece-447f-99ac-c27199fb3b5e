<template>
  <div class="flex flex-col items-center justify-center min-h-screen bg-white">
    <div class="w-full max-w-2xl p-6 bg-gray-100 rounded shadow">
      <h2 class="text-xl font-bold mb-4">面试总结</h2>
      <pre class="bg-white p-4 rounded text-sm overflow-x-auto mb-6 border border-gray-200">
{{ summaryJson }}
      </pre>
      <div class="flex justify-end">
        <button
          class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
          @click="restart"
        >
          重新开始
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
import { onMounted, ref } from 'vue';

const router = useRouter();
const summaryJson = ref('');

onMounted(() => {
  // 从 localStorage 获取问答记录
  const data = localStorage.getItem('interview_records');
  summaryJson.value = data ? JSON.stringify(JSON.parse(data), null, 2) : '暂无记录';
});

function restart() {
  localStorage.removeItem('interview_records');
  router.push('/');
}
</script>
