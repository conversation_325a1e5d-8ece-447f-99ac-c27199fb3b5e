import axios from 'axios';
import { ElMessage } from 'element-plus';

// 创建axios实例
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api',
  timeout: 30000, // 30秒超时
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token等
    console.log('API请求:', config.method?.toUpperCase(), config.url, config.data);
    return config;
  },
  (error) => {
    console.error('请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    console.log('API响应:', response.status, response.data);
    return response;
  },
  (error) => {
    console.error('API错误:', error);
    
    // 统一错误处理
    let errorMessage = '网络请求失败，请稍后重试';
    
    if (error.response) {
      // 服务器返回错误状态码
      const { status, data } = error.response;
      
      switch (status) {
        case 400:
          errorMessage = data.error?.message || '请求参数错误';
          break;
        case 404:
          errorMessage = '请求的资源不存在';
          break;
        case 500:
          errorMessage = data.error?.message || '服务器内部错误';
          break;
        case 503:
          errorMessage = 'AI服务暂时不可用，请稍后重试';
          break;
        default:
          errorMessage = `请求失败 (${status})`;
      }
    } else if (error.request) {
      // 网络错误
      errorMessage = '网络连接失败，请检查网络设置';
    }
    
    // 显示错误提示
    ElMessage.error(errorMessage);
    
    return Promise.reject(error);
  }
);

/**
 * 面试API服务
 */
export const interviewAPI = {
  /**
   * 开始新的面试会话
   * @param {Object} params - 面试参数
   * @param {string} params.interviewStyle - 面试风格 (professional/friendly/campus)
   * @param {string} params.candidateName - 候选人姓名
   * @returns {Promise} 面试会话信息
   */
  async startInterview(params = {}) {
    try {
      const response = await apiClient.post('/interview/start', {
        interviewStyle: params.interviewStyle || 'professional',
        candidateName: params.candidateName || '面试者',
      });
      
      return response.data;
    } catch (error) {
      throw new Error('开始面试失败');
    }
  },

  /**
   * 分析用户回答并获取AI反馈
   * @param {Object} params - 分析参数
   * @param {number} params.questionIndex - 问题索引
   * @param {string} params.question - 问题内容
   * @param {string} params.answer - 用户回答
   * @param {string} params.sessionId - 会话ID
   * @param {string} params.interviewStyle - 面试风格
   * @returns {Promise} AI分析结果
   */
  async analyzeAnswer(params) {
    try {
      const response = await apiClient.post('/interview/analyze', {
        questionIndex: params.questionIndex,
        question: params.question,
        answer: params.answer,
        sessionId: params.sessionId,
        interviewStyle: params.interviewStyle || 'professional',
      });
      
      return response.data;
    } catch (error) {
      throw new Error('分析回答失败');
    }
  },

  /**
   * 获取面试总结
   * @param {string} sessionId - 会话ID
   * @returns {Promise} 面试总结
   */
  async getInterviewSummary(sessionId) {
    try {
      const response = await apiClient.get(`/interview/summary/${sessionId}`);
      return response.data;
    } catch (error) {
      throw new Error('获取面试总结失败');
    }
  },
};

/**
 * 模拟API响应（用于开发测试）
 */
export const mockAPI = {
  /**
   * 模拟开始面试
   */
  async startInterview(params = {}) {
    await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟网络延迟
    
    return {
      success: true,
      data: {
        sessionId: `session_${Date.now()}`,
        firstQuestion: '你最近完成的一件最有成就感的事是什么？你在其中扮演了什么角色？',
        totalQuestions: 3,
        estimatedDuration: '5-8分钟',
      },
      timestamp: new Date().toISOString(),
    };
  },

  /**
   * 模拟分析回答
   */
  async analyzeAnswer(params) {
    await new Promise(resolve => setTimeout(resolve, 2000)); // 模拟AI处理时间
    
    const responses = [
      {
        action: 'follow_up',
        feedback: '很好的回答！能再详细说说你在项目中遇到的具体挑战吗？',
        nextQuestion: null,
        shouldContinue: true,
        analysis: {
          completeness: 7,
          clarity: 8,
          relevance: 9,
          suggestions: ['可以更详细描述具体的技术挑战', '建议量化项目成果'],
        },
      },
      {
        action: 'next_question',
        feedback: '明白了，谢谢你的详细回答。',
        nextQuestion: '请讲讲一次你解决冲突或困难的经历。',
        shouldContinue: true,
        analysis: {
          completeness: 9,
          clarity: 9,
          relevance: 10,
          suggestions: [],
        },
      },
      {
        action: 'interview_complete',
        feedback: '感谢您参与本次面试，您的回答都很出色！',
        nextQuestion: null,
        shouldContinue: false,
        analysis: {
          overallScore: 85,
          strengths: ['沟通能力强', '技术基础扎实', '团队协作意识好'],
          improvements: ['可以更多展示领导力', '建议准备更多具体案例'],
        },
      },
    ];
    
    // 根据问题索引和回答长度决定响应类型
    let responseIndex = 0;
    if (params.questionIndex >= 2) {
      responseIndex = 2; // 面试结束
    } else if (params.answer && params.answer.length > 50) {
      responseIndex = 1; // 下一题
    } else {
      responseIndex = 0; // 追问
    }
    
    return {
      success: true,
      data: responses[responseIndex],
      timestamp: new Date().toISOString(),
    };
  },

  /**
   * 模拟获取面试总结
   */
  async getInterviewSummary(sessionId) {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return {
      success: true,
      data: {
        sessionId,
        candidateName: '面试者',
        interviewStyle: 'professional',
        startTime: new Date(Date.now() - 8 * 60 * 1000).toISOString(),
        endTime: new Date().toISOString(),
        duration: '8分钟',
        overallScore: 85,
        questions: [
          {
            questionIndex: 0,
            question: '你最近完成的一件最有成就感的事是什么？',
            answer: '我最近完成了一个团队项目，负责前端开发...',
            followUps: [
              {
                question: '能详细说说遇到的挑战吗？',
                answer: '主要挑战是技术选型和团队协作...',
              },
            ],
            analysis: {
              completeness: 9,
              clarity: 8,
              relevance: 9,
            },
          },
        ],
        summary: {
          strengths: ['沟通能力强', '技术基础扎实', '学习能力强'],
          improvements: ['可以更多展示领导力', '建议准备更多具体案例'],
          recommendation: '建议进入下一轮面试',
        },
      },
      timestamp: new Date().toISOString(),
    };
  },
};

/**
 * 根据环境变量决定使用真实API还是模拟API
 */
const isDevelopment = import.meta.env.MODE === 'development';
const useMockAPI = import.meta.env.VITE_USE_MOCK_API === 'true' || isDevelopment;

export const api = useMockAPI ? mockAPI : interviewAPI;

export default api;
