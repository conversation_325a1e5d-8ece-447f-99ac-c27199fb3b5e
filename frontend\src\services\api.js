import axios from 'axios';
import { ElMessage } from 'element-plus';

// 创建axios实例
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api',
  timeout: 30000, // 30秒超时
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token等
    console.log('API请求:', config.method?.toUpperCase(), config.url, config.data);
    return config;
  },
  (error) => {
    console.error('请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    console.log('API响应:', response.status, response.data);
    return response;
  },
  (error) => {
    console.error('API错误:', error);
    
    // 统一错误处理
    let errorMessage = '网络请求失败，请稍后重试';
    
    if (error.response) {
      // 服务器返回错误状态码
      const { status, data } = error.response;
      
      switch (status) {
        case 400:
          errorMessage = data.error?.message || '请求参数错误';
          break;
        case 404:
          errorMessage = '请求的资源不存在';
          break;
        case 500:
          errorMessage = data.error?.message || '服务器内部错误';
          break;
        case 503:
          errorMessage = 'AI服务暂时不可用，请稍后重试';
          break;
        default:
          errorMessage = `请求失败 (${status})`;
      }
    } else if (error.request) {
      // 网络错误
      errorMessage = '网络连接失败，请检查网络设置';
    }
    
    // 显示错误提示
    ElMessage.error(errorMessage);
    
    return Promise.reject(error);
  }
);

/**
 * 面试API服务（简化版）
 */
export const interviewAPI = {
  /**
   * 分析用户回答并获取AI反馈（唯一的后端接口）
   * @param {Object} params - 分析参数
   * @param {string} params.question - 问题内容
   * @param {string} params.answer - 用户回答
   * @returns {Promise} AI分析结果
   */
  async analyzeAnswer(params) {
    try {
      const response = await apiClient.post('/analyze', {
        question: params.question,
        answer: params.answer,
      });

      return response.data;
    } catch (error) {
      throw new Error('分析回答失败');
    }
  },
};

/**
 * 模拟API响应（用于开发测试）
 */
export const mockAPI = {
  /**
   * 模拟分析回答（唯一接口）
   */
  async analyzeAnswer(params) {
    await new Promise(resolve => setTimeout(resolve, 2000)); // 模拟AI处理时间

    // 简单的模拟逻辑：根据回答长度决定是否追问
    const needFollowUp = params.answer && params.answer.length < 50;

    return {
      success: true,
      data: {
        needFollowUp,
        feedback: needFollowUp
          ? '很好的回答！能再详细说说吗？'
          : '明白了，谢谢你的详细回答。'
      },
      timestamp: new Date().toISOString(),
    };
  },
};

/**
 * 根据环境变量决定使用真实API还是模拟API
 */
const useMockAPI = import.meta.env.VITE_USE_MOCK_API === 'true';

export const api = useMockAPI ? mockAPI : interviewAPI;

export default api;
