# API集成功能测试指南

## 测试概述

本指南用于测试新增的前后端交互功能，包括AI智能反馈、追问机制和面试流程控制。

## 当前配置

- **模拟API模式**: 已启用（开发环境）
- **API基础URL**: `http://localhost:3000/api`
- **模拟响应**: 根据回答内容智能决定追问或下一题

## 测试步骤

### 1. 欢迎页面测试
- [ ] 打开 `http://localhost:5174/`
- [ ] 验证欢迎页面正常显示
- [ ] 点击"开始面试"按钮
- [ ] 验证跳转到面试页面

### 2. 面试初始化测试
- [ ] 验证面试页面显示"正在初始化面试..."加载状态
- [ ] 验证成功显示第一个问题
- [ ] 验证进度条显示"第1题/共3题"
- [ ] 验证问题内容正确显示

### 3. 语音识别测试
- [ ] 点击"点击开始语音作答"按钮
- [ ] 验证按钮变为"正在录音中..."状态
- [ ] 验证显示波浪动画
- [ ] 说话测试语音识别（需要Chrome浏览器）
- [ ] 验证识别结果显示在文本框中

### 4. 手动输入测试
- [ ] 在文本框中直接输入回答
- [ ] 验证字数限制（1000字符）
- [ ] 验证"提交回答"按钮在有内容时启用

### 5. AI反馈测试

#### 5.1 触发追问（输入较短回答）
- [ ] 输入简短回答（如："我做了一个项目"）
- [ ] 点击"提交回答"
- [ ] 验证显示"正在分析您的回答..."加载状态
- [ ] 验证收到追问反馈（如："能再详细说说..."）
- [ ] 验证问题标签变为"面试官追问："
- [ ] 验证按钮变为"提交追问回答"

#### 5.2 触发下一题（输入详细回答）
- [ ] 输入详细回答（超过50字符）
- [ ] 点击"提交回答"
- [ ] 验证收到确认反馈（如："明白了，谢谢你的回答"）
- [ ] 验证自动进入下一题
- [ ] 验证进度条更新

#### 5.3 面试结束
- [ ] 完成第3题的回答
- [ ] 验证显示"面试完成！正在跳转到总结页面..."
- [ ] 验证自动跳转到总结页面

### 6. 总结页面测试
- [ ] 验证显示"面试完成！"徽章
- [ ] 验证统计卡片显示正确数据：
  - 问题总数
  - 回答字数
  - 整体评价
- [ ] 验证详细记录展示：
  - 问题内容
  - 用户回答
  - 追问记录（如有）
  - 系统反馈
- [ ] 测试"下载记录"功能
- [ ] 测试"重新开始"功能

### 7. 错误处理测试
- [ ] 断开网络连接，测试错误提示
- [ ] 输入空回答，验证提示信息
- [ ] 快速连续点击按钮，验证防重复提交

### 8. 响应式测试
- [ ] 在不同屏幕尺寸下测试界面
- [ ] 在移动设备上测试触摸交互
- [ ] 验证所有功能在移动端正常工作

## 模拟API行为说明

### 追问逻辑
- 回答长度 ≤ 50字符 → 触发追问
- 回答长度 > 50字符 → 进入下一题
- 第3题完成后 → 面试结束

### 模拟反馈内容
1. **追问反馈**：
   - "很好的回答！能再详细说说你在项目中遇到的具体挑战吗？"
   
2. **确认反馈**：
   - "明白了，谢谢你的详细回答。"
   
3. **结束反馈**：
   - "感谢您参与本次面试，您的回答都很出色！"

### 分析数据
- **完整度评分**: 7-9分
- **清晰度评分**: 8-9分
- **相关性评分**: 9-10分
- **改进建议**: 根据回答内容提供

## 预期结果

### 成功标准
- [ ] 所有页面正常加载和跳转
- [ ] API调用成功，显示正确的加载状态
- [ ] AI反馈逻辑正确工作（追问/下一题/结束）
- [ ] 数据正确保存和展示
- [ ] 错误处理机制正常工作
- [ ] 用户体验流畅，无明显卡顿

### 性能指标
- [ ] 页面加载时间 < 2秒
- [ ] API响应时间 < 3秒（模拟延迟）
- [ ] 动画流畅，无卡顿
- [ ] 内存使用正常，无内存泄漏

## 故障排除

### 常见问题
1. **语音识别不工作**
   - 确保使用Chrome浏览器
   - 检查麦克风权限
   - 使用手动输入作为备选

2. **API调用失败**
   - 检查网络连接
   - 确认环境变量配置
   - 查看浏览器控制台错误信息

3. **页面显示异常**
   - 清除浏览器缓存
   - 检查CSS样式加载
   - 验证组件导入路径

### 调试工具
- 浏览器开发者工具
- Vue DevTools
- Network面板查看API请求
- Console面板查看错误信息

## 下一步计划

### 后端集成
1. 实现真实的后端API服务
2. 集成大语言模型（如通义千问）
3. 添加用户认证和会话管理
4. 实现数据持久化存储

### 功能增强
1. 添加面试风格选择
2. 支持多轮追问
3. 实现实时语音转文字
4. 添加面试评分算法

### 部署优化
1. 配置生产环境API地址
2. 优化构建配置
3. 添加CDN加速
4. 实现自动化部署
