# 故障排除指南

## 常见问题和解决方案

### 1. process is not defined 错误

**错误信息**: `Uncaught ReferenceError: process is not defined`

**原因**: 在浏览器环境中使用了 Node.js 的 `process` 对象

**解决方案**: 
- 使用 `import.meta.env` 替代 `process.env`
- 使用 `import.meta.env.MODE` 替代 `process.env.NODE_ENV`

```javascript
// ❌ 错误写法
const apiUrl = process.env.VITE_API_BASE_URL;
const isDev = process.env.NODE_ENV === 'development';

// ✅ 正确写法
const apiUrl = import.meta.env.VITE_API_BASE_URL;
const isDev = import.meta.env.MODE === 'development';
```

### 2. 语音识别不工作

**错误信息**: 语音识别功能无响应或报错

**可能原因**:
- 浏览器不支持 Web Speech API
- 麦克风权限未授予
- HTTPS 要求（某些浏览器）

**解决方案**:
1. 使用 Chrome 浏览器（推荐）
2. 确保授予麦克风权限
3. 在 HTTPS 环境下使用
4. 使用手动输入作为备选方案

### 3. API 调用失败

**错误信息**: 网络请求失败或超时

**可能原因**:
- 后端服务未启动
- API 地址配置错误
- 网络连接问题
- CORS 跨域问题

**解决方案**:
1. 检查 `.env.development` 文件中的 API 配置
2. 确认后端服务正在运行
3. 检查网络连接
4. 当前使用模拟 API，无需后端服务

### 4. 样式显示异常

**错误信息**: 页面样式混乱或组件显示不正常

**可能原因**:
- Element Plus 样式未正确加载
- CSS 冲突
- Tailwind CSS 配置问题

**解决方案**:
1. 清除浏览器缓存
2. 重启开发服务器
3. 检查 Element Plus 导入是否正确
4. 验证 CSS 文件路径

### 5. 路由跳转问题

**错误信息**: 页面跳转失败或显示 404

**可能原因**:
- Vue Router 配置错误
- 组件路径错误
- 路由守卫问题

**解决方案**:
1. 检查路由配置文件
2. 验证组件导入路径
3. 检查浏览器控制台错误信息

## 开发环境设置

### 环境变量配置

确保 `.env.development` 文件存在并包含正确配置：

```bash
# API基础URL
VITE_API_BASE_URL=http://localhost:3000/api

# 是否使用模拟API
VITE_USE_MOCK_API=true

# 应用标题
VITE_APP_TITLE=Kora 语音面试官

# 调试模式
VITE_DEBUG=true
```

### 依赖安装

如果遇到依赖问题，尝试重新安装：

```bash
# 删除 node_modules 和 package-lock.json
rm -rf node_modules package-lock.json

# 重新安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 浏览器兼容性

**推荐浏览器**:
- Chrome 80+ (完整功能支持)
- Firefox 75+ (基本功能)
- Safari 13+ (基本功能)
- Edge 80+ (基本功能)

**功能支持**:
- 语音识别: 主要支持 Chrome
- 其他功能: 所有现代浏览器

## 调试工具

### 1. 浏览器开发者工具
- **Console**: 查看错误信息和日志
- **Network**: 检查 API 请求和响应
- **Application**: 查看本地存储数据
- **Sources**: 调试 JavaScript 代码

### 2. Vue DevTools
安装 Vue DevTools 浏览器扩展来调试 Vue 组件和状态。

### 3. 日志输出
在开发环境中，API 调用会在控制台输出详细日志：

```javascript
// API请求日志
console.log('API请求:', method, url, data);

// API响应日志
console.log('API响应:', status, data);
```

## 性能优化

### 1. 开发环境优化
- 使用 Vite 的热重载功能
- 启用 Vue DevTools
- 使用模拟 API 减少网络延迟

### 2. 生产环境优化
- 构建前检查所有环境变量
- 启用代码压缩和优化
- 配置 CDN 加速

## 联系支持

如果遇到无法解决的问题：

1. 检查浏览器控制台的完整错误信息
2. 查看网络请求的详细信息
3. 记录重现问题的具体步骤
4. 提供浏览器版本和操作系统信息

## 更新日志

### v1.1.0 (当前版本)
- ✅ 修复 `process is not defined` 错误
- ✅ 优化环境变量处理
- ✅ 改进错误处理机制
- ✅ 添加完整的故障排除指南

### v1.0.0
- ✅ 基础面试功能
- ✅ 语音识别支持
- ✅ Element Plus UI 集成
- ✅ 响应式设计
