
<template>
  <div class="flex flex-col items-center justify-center min-h-screen bg-white">
    <el-card class="w-full max-w-xl p-6">
      <h2 class="text-xl font-bold mb-4">面试问答</h2>
      <el-alert
        :title="`问题：${currentQuestion}`"
        type="info"
        class="mb-6"
        show-icon
        :closable="false"
      />
      <div class="mb-4 flex gap-2">
        <el-button
          type="primary"
          :loading="isRecording"
          :disabled="isRecording"
          @click="startRecognition"
        >
          {{ isRecording ? '正在录音...' : '开始语音作答' }}
        </el-button>
        <el-button
          v-if="isRecording"
          type="warning"
          @click="stopRecognition"
        >
          停止
        </el-button>
      </div>
      <el-form label-width="80px" v-if="answerText">
        <el-form-item label="识别结果">
          <el-input type="textarea" :model-value="answerText" readonly autosize />
        </el-form-item>
      </el-form>
      <el-alert
        v-if="feedback"
        :title="`系统反馈：${feedback}`"
        type="success"
        class="mb-4"
        show-icon
        :closable="false"
      />
      <div class="flex justify-end">
        <el-button
          v-if="answerText && !isRecording"
          type="success"
          @click="nextQuestion"
        >
          下一题
        </el-button>
      </div>
    </el-card>
  </div>
</template>


<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import 'element-plus/dist/index.css';
import { ElButton, ElCard, ElAlert, ElForm, ElFormItem, ElInput } from 'element-plus';

const questions = [
  '你最近完成的一件最有成就感的事是什么？你在其中扮演了什么角色？',
  '请讲讲一次你解决冲突或困难的经历。',
  '如果你加入一个你不熟悉的项目团队，你会如何快速融入？',
];
const feedbacks = [
  '明白了，谢谢你的回答。',
  '好的，能再详细说说你的具体做法吗？',
  '谢谢，接下来请听下一题。',
];

const current = ref(0);
const currentQuestion = ref(questions[current.value]);
const answerText = ref('');
const feedback = ref('');
const isRecording = ref(false);
let recognition = null;

// 记录所有问答
const records = ref([]);

const router = useRouter();

function startRecognition() {
  answerText.value = '';
  feedback.value = '';
  isRecording.value = true;
  if (!('webkitSpeechRecognition' in window)) {
    alert('当前浏览器不支持语音识别。请使用 Chrome 浏览器。');
    isRecording.value = false;
    return;
  }
  recognition = new window.webkitSpeechRecognition();
  recognition.lang = 'zh-CN';
  recognition.continuous = false;
  recognition.interimResults = false;
  recognition.onresult = (event) => {
    answerText.value = event.results[0][0].transcript;
    isRecording.value = false;
    // 简单模拟反馈
    feedback.value = feedbacks[Math.floor(Math.random() * feedbacks.length)];
  };
  recognition.onerror = () => {
    isRecording.value = false;
    feedback.value = '语音识别出错，请重试。';
  };
  recognition.onend = () => {
    isRecording.value = false;
  };
  recognition.start();
}

function stopRecognition() {
  if (recognition) {
    recognition.stop();
    isRecording.value = false;
  }
}

function nextQuestion() {
  // 保存当前问答
  records.value.push({
    question: currentQuestion.value,
    answer: answerText.value,
    feedback: feedback.value,
  });
  // 存储到 localStorage
  localStorage.setItem('interview_records', JSON.stringify(records.value));

  if (current.value < questions.length - 1) {
    current.value++;
    currentQuestion.value = questions[current.value];
    answerText.value = '';
    feedback.value = '';
  } else {
    router.push('/summary');
  }
}
</script>
