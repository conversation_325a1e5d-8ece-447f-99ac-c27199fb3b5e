from fastapi import FastAPI
from langchain_openai import ChatOpenAI
from loguru import logger

app = FastAPI(prefix="/api")

@app.post("/analyze")
async def analyze_answer(question: str, answer: str):
    logger.info(f"问题: {question}，回答: {answer}")
    return {
        "success": True,
        "data": {
            "needFollowUp": True,
            "feedback": "很好的回答！能再详细说说你在项目中遇到的具体挑战吗？"
        },
        "timestamp": "2024-01-20T10:30:00Z"
    }