{"name": "kora-interview-backend", "version": "1.0.0", "description": "Kora语音面试官后端API服务", "main": "backend-example.js", "scripts": {"start": "node backend-example.js", "dev": "nodemon backend-example.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["interview", "ai", "voice", "api"], "author": "Kora Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "axios": "^1.6.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}