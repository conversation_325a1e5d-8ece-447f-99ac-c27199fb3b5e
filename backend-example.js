// 简化的后端实现示例
// 只包含一个分析接口

const express = require('express');
const cors = require('cors');

const app = express();

// 中间件
app.use(cors());
app.use(express.json());

// 唯一的API接口：分析用户回答
app.post('/api/analyze', async (req, res) => {
  try {
    const { question, answer } = req.body;
    
    // 参数验证
    if (!question || !answer) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_PARAMS',
          message: '问题和回答内容不能为空'
        }
      });
    }

    console.log('收到分析请求:');
    console.log('问题:', question);
    console.log('回答:', answer);
    
    // 调用AI分析函数
    const result = await analyzeAnswer(question, answer);
    
    res.json({
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('分析回答失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'AI_SERVICE_ERROR',
        message: 'AI服务暂时不可用，请稍后重试'
      }
    });
  }
});

// AI分析函数
async function analyzeAnswer(question, answer) {
  // 这里集成您的大模型API
  // 以下是示例实现，您需要替换为真实的AI调用
  
  try {
    // 方案1: 调用通义千问API
    // const result = await callQwenAPI(question, answer);
    
    // 方案2: 调用其他大模型API
    // const result = await callOtherLLM(question, answer);
    
    // 方案3: 简单的规则判断（临时方案）
    const result = simpleAnalysis(question, answer);
    
    return result;
    
  } catch (error) {
    console.error('AI分析失败:', error);
    
    // 降级处理：简单规则判断
    return simpleAnalysis(question, answer);
  }
}

// 简单的分析逻辑（作为降级方案）
function simpleAnalysis(question, answer) {
  const answerLength = answer.trim().length;
  
  // 根据回答长度判断是否需要追问
  if (answerLength < 30) {
    return {
      needFollowUp: true,
      feedback: "能再详细说说吗？比如具体的过程和结果。"
    };
  } else if (answerLength < 80) {
    return {
      needFollowUp: true,
      feedback: "很好的回答！能再补充一些具体的细节或者你的感受吗？"
    };
  } else {
    return {
      needFollowUp: false,
      feedback: "明白了，谢谢你的详细回答。"
    };
  }
}

// 通义千问API调用示例（需要配置API密钥）
async function callQwenAPI(question, answer) {
  const axios = require('axios');
  
  const prompt = `
作为专业的面试官，请分析候选人的回答并决定是否需要追问。

问题：${question}
回答：${answer}

请根据回答质量决定：
1. 如果回答不够详细或需要更多信息，返回追问问题
2. 如果回答充分满意，返回确认反馈

返回JSON格式：
{
  "needFollowUp": true/false,
  "feedback": "追问问题或确认反馈"
}
  `;

  try {
    const response = await axios.post(
      'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation',
      {
        model: "qwen-turbo",
        input: { prompt },
        parameters: {
          temperature: 0.7,
          max_tokens: 200
        }
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.DASHSCOPE_API_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    return JSON.parse(response.data.output.text);
  } catch (error) {
    console.error('调用通义千问API失败:', error);
    throw error;
  }
}

// 启动服务器
const PORT = process.env.PORT || 8000;
app.listen(PORT, () => {
  console.log(`🚀 后端服务器运行在 http://localhost:${PORT}`);
  console.log(`📡 API接口: http://localhost:${PORT}/api/analyze`);
  console.log('');
  console.log('📋 接口说明:');
  console.log('POST /api/analyze');
  console.log('请求体: { "question": "问题", "answer": "回答" }');
  console.log('响应: { "success": true, "data": { "needFollowUp": boolean, "feedback": "反馈" } }');
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭服务器...');
  process.exit(0);
});

module.exports = app;
