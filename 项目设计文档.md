任务目标：
请你构建一个简易的“中文语音面试官”网页Demo，用于模拟一段约5分钟的结构化行为面试。我们希望看到你如何将语音识别、LLM交互、网页前端结合起来，做出一个完整的MVP。
你需要实现的功能：
1.打开网页后，展示欢迎语：“你好，我是Kora的语音面试官，接下来我会用中文向你提问一些常见面试问题，请用语音作答。”
2.系统依次提问3个行为类面试问题，如：
1.你最近完成的一件最有成就感的事是什么？你在其中扮演了什么角色？
2.请讲讲一次你解决冲突或困难的经历。
3.如果你加入一个你不熟悉的项目团队，你会如何快速融入？
1.用户每次用中文语音回答，系统识别后可将其文字显示在页面上。
2.每次用户回答完，系统可进行简单的追问或反馈（可由LLM生成简答提示或说“明白了，谢谢你的回答”）。
3.最后生成一个总结页面，展示面试官提问+用户回答的记录（JSON结构或可视化）。
4.请参考Mercor等美国公司的AI面试官体验（但不需要实现类似其动画效果，我们测试任务只需要语音即可）
技术要求建议（可自由发挥）：
前端框架：React；
可使用 Web Speech API / Whisper API / 科大讯飞 等实现中文语音识别；
LLM 可调用 同义千问或其它开源模型；
UI不需复杂，但建议结构清晰，交互顺畅；
需要部署成可访问的网页（Netlify / Vercel / Github Pages 均可）；
请附带 GitHub 项目链接 + 在线演示链接。
✅ Bonus 加分项：
使用 prompt 控制 LLM 生成 follow-up 问题；
面试风格可定制（严肃/亲切/校园风等）；
支持文本作答作为备选（若语音失败时）；
UI 能适配移动端。

